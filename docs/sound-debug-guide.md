# 声音播放调试指南

## 问题描述
使用 `playErrorSoundSmart` 方法时声音无法播放。

## 调试步骤

### 1. 访问调试页面
访问 `/debug/sound` 路径，打开声音调试页面。

### 2. 检查基础设置
在调试页面中检查以下设置：
- **声音开关**: 确保声音已开启
- **声音类型**: 确认声音类型设置（理性/活泼）
- **本地存储状态**: 查看 localStorage 中的设置值

### 3. 测试不同方法
按顺序测试以下按钮：

1. **测试直接方法**: 使用基础的 `playErrorSound` 方法
2. **测试 Smart 方法**: 使用 `playErrorSoundSmart` 方法
3. **测试单个错误**: 播放单个错误音频
4. **测试多个错误**: 播放多个错误音频序列

### 4. 查看调试日志
在页面底部的"调试日志"区域查看详细的调试信息，包括：
- 方法调用参数
- 本地存储状态
- 音频文件加载状态
- 播放过程中的错误信息

### 5. 浏览器控制台
打开浏览器开发者工具的控制台，查看更详细的调试信息：
- 🔊 开头的日志：声音播放相关信息
- ❌ 开头的日志：错误信息
- ✅ 开头的日志：成功信息
- ⏰ 开头的日志：定时器相关信息

## 常见问题及解决方案

### 1. 声音被禁用
**现象**: 调试日志显示"声音已禁用，跳过播放"
**解决**: 在调试页面中开启声音开关，或检查本地存储中的 `soundEnabled` 值

### 2. 音频文件加载失败
**现象**: 控制台显示音频加载失败的错误
**解决**: 
- 检查音频文件路径是否正确
- 确认音频文件是否存在于 `/resource/sounds/` 目录
- 检查网络连接和服务器配置

### 3. 防抖机制阻止播放
**现象**: 调试日志显示"检测到重复的错误推送，忽略播放"
**解决**: 
- 等待 2 秒后再次尝试
- 或者使用不同的错误类型进行测试

### 4. 浏览器自动播放策略
**现象**: 音频文件加载成功但无法播放
**解决**: 
- 确保用户已与页面进行过交互（点击、滚动等）
- 在浏览器设置中允许自动播放音频

### 5. 音频元素未初始化
**现象**: 调试日志显示"音频元素未初始化"
**解决**: 
- 检查 `initAudioElements` 方法是否正常执行
- 确认音频文件路径配置正确

## 调试信息说明

### 本地存储键值
- `soundEnabled`: 声音开关状态 (true/false)
- `soundType`: 声音类型 ('rational'/'lively')

### 错误类型映射
- `1`: 双手作业
- `2`: 垂直作业面  
- `8`: 手套识别
- `21`: 堵盖大小判定
- `22`: 堵盖贴合判定
- 等等...

### 音频文件路径格式
```
/resource/sounds/{soundType}/{category}/{filename}.mp3
```

例如：
- 理性语音手套识别: `/resource/sounds/rational/wear-gloves.mp3`
- 活泼语音双手作业: `/resource/sounds/lively/tightening/both-hands.mp3`

## 代码修改说明

为了帮助调试，我在以下文件中添加了详细的调试日志：

1. **src/composables/useSmartErrorSound.ts**
   - 添加了参数检查和状态日志
   - 显示本地存储状态
   - 记录防抖和去重逻辑

2. **src/composables/useErrorSound.ts**
   - 添加了音频元素初始化日志
   - 记录播放队列状态
   - 显示音频文件加载状态

3. **src/views/SoundDebugTest.vue**
   - 创建了专门的调试页面
   - 提供了多种测试方法
   - 实时显示调试日志

## 恢复生产环境

调试完成后，如果需要移除调试日志：

1. 删除或注释掉 `console.log` 语句
2. 移除调试页面路由配置
3. 删除 `SoundDebugTest.vue` 文件

## 联系支持

如果问题仍然存在，请提供以下信息：
- 浏览器类型和版本
- 调试页面的日志输出
- 浏览器控制台的错误信息
- 网络请求状态（开发者工具 Network 标签）
