<template>
  <div class="sound-debug-page">
    <div class="page-header">
      <h1>声音播放调试页面</h1>
      <p>用于调试 playErrorSoundSmart 方法的声音播放问题</p>
    </div>

    <div class="debug-panel">
      <div class="settings-section">
        <h3>当前设置</h3>
        <div class="setting-item">
          <label>声音开关:</label>
          <a-switch v-model:checked="soundEnabled" @change="handleSoundToggle" />
          <span>{{ soundEnabled ? '已开启' : '已关闭' }}</span>
        </div>
        <div class="setting-item">
          <label>声音类型:</label>
          <a-select v-model:value="soundType" style="width: 120px" @change="handleSoundTypeChange">
            <a-select-option value="rational">理性语音</a-select-option>
            <a-select-option value="lively">活泼语音</a-select-option>
          </a-select>
        </div>
        <div class="setting-item">
          <label>本地存储状态:</label>
          <div class="storage-info">
            <div>soundEnabled: {{ storageInfo.soundEnabled }}</div>
            <div>soundType: {{ storageInfo.soundType }}</div>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h3>测试按钮</h3>
        <div class="button-group">
          <a-button type="primary" @click="testSingleError" :loading="isPlaying">
            测试单个错误 (手套识别)
          </a-button>
          <a-button @click="testMultipleErrors" :loading="isPlaying">
            测试多个错误
          </a-button>
          <a-button @click="testSmartMethod" :loading="isPlaying">
            测试 Smart 方法
          </a-button>
          <a-button @click="testDirectMethod" :loading="isPlaying">
            测试直接方法
          </a-button>
          <a-button danger @click="stopPlayback" :disabled="!isPlaying">
            停止播放
          </a-button>
        </div>
      </div>

      <div class="status-section">
        <h3>播放状态</h3>
        <div class="status-info">
          <div>正在播放: {{ isPlaying ? '是' : '否' }}</div>
          <div>音频元素数量: {{ audioElementsCount }}</div>
        </div>
      </div>

      <div class="logs-section">
        <h3>调试日志</h3>
        <div class="logs-container">
          <div v-for="(log, index) in logs" :key="index" class="log-item">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
        <a-button @click="clearLogs" size="small">清空日志</a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useErrorSound, type SoundType } from '/@/composables/useErrorSound';
import { useSmartErrorSound } from '/@/composables/useSmartErrorSound';
import { createLocalStorage } from '/@/utils/cache';
import { message } from 'ant-design-vue';

const { playErrorSound, stopCurrentPlayback, isPlaying } = useErrorSound();
const { playErrorSoundSmart } = useSmartErrorSound();

const soundEnabled = ref<boolean>(true);
const soundType = ref<SoundType>('rational');
const logs = ref<Array<{ time: string, message: string }>>([]);

const ls = createLocalStorage();

// 存储信息
const storageInfo = computed(() => ({
  soundEnabled: ls.get('soundEnabled'),
  soundType: ls.get('soundType')
}));

// 音频元素数量（模拟，实际需要从 useErrorSound 获取）
const audioElementsCount = ref(0);

const addLog = (message: string) => {
  const time = new Date().toLocaleTimeString();
  logs.value.unshift({ time, message });
  if (logs.value.length > 100) {
    logs.value = logs.value.slice(0, 100);
  }
};

const handleSoundToggle = (checked: boolean) => {
  ls.set('soundEnabled', checked);
  addLog(`声音开关: ${checked ? '开启' : '关闭'}`);
  message.success(checked ? '声音已开启' : '声音已关闭');
};

const handleSoundTypeChange = (type: SoundType) => {
  ls.set('soundType', type);
  addLog(`声音类型切换: ${type}`);
  message.success(`已切换到${type === 'rational' ? '理性' : '活泼'}语音`);
};

const testSingleError = () => {
  addLog('测试单个错误 - 手套识别 (actionType: 8)');
  const errors = [{ actionType: 8 }];
  playErrorSound(errors, soundType.value);
};

const testMultipleErrors = () => {
  addLog('测试多个错误 - 双手作业、垂直作业面、手套识别');
  const errors = [
    { actionType: 1 }, // 双手作业
    { actionType: 2 }, // 垂直作业面
    { actionType: 8 }  // 手套识别
  ];
  playErrorSound(errors, soundType.value);
};

const testSmartMethod = () => {
  addLog('测试 Smart 方法 - playErrorSoundSmart');
  const errors = [
    { actionType: 1 }, // 双手作业
    { actionType: 8 }  // 手套识别
  ];
  playErrorSoundSmart(errors, soundType.value);
};

const testDirectMethod = () => {
  addLog('测试直接方法 - playErrorSound');
  const errors = [
    { actionType: 1 }, // 双手作业
    { actionType: 8 }  // 手套识别
  ];
  playErrorSound(errors, soundType.value);
};

const stopPlayback = () => {
  addLog('手动停止播放');
  stopCurrentPlayback();
};

const clearLogs = () => {
  logs.value = [];
};

// 初始化设置
onMounted(() => {
  soundEnabled.value = ls.get('soundEnabled') ?? true;
  soundType.value = ls.get('soundType') ?? 'rational';
  
  addLog('页面初始化完成');
  addLog(`当前设置 - 声音: ${soundEnabled.value ? '开启' : '关闭'}, 类型: ${soundType.value}`);
  
  // 监听控制台输出
  const originalLog = console.log;
  const originalError = console.error;
  const originalWarn = console.warn;
  
  console.log = (...args) => {
    if (args[0] && typeof args[0] === 'string' && args[0].includes('🔊')) {
      addLog(`[LOG] ${args.join(' ')}`);
    }
    originalLog.apply(console, args);
  };
  
  console.error = (...args) => {
    if (args[0] && typeof args[0] === 'string' && args[0].includes('❌')) {
      addLog(`[ERROR] ${args.join(' ')}`);
    }
    originalError.apply(console, args);
  };
  
  console.warn = (...args) => {
    if (args[0] && typeof args[0] === 'string' && args[0].includes('⚠️')) {
      addLog(`[WARN] ${args.join(' ')}`);
    }
    originalWarn.apply(console, args);
  };
});
</script>

<style scoped>
.sound-debug-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
}

.debug-panel {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.settings-section,
.test-section,
.status-section,
.logs-section {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.logs-section {
  grid-column: 1 / -1;
}

.setting-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  gap: 10px;
}

.setting-item label {
  min-width: 80px;
  font-weight: 500;
}

.storage-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
  font-family: monospace;
  font-size: 12px;
  background: #fff;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.logs-container {
  max-height: 300px;
  overflow-y: auto;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 10px;
}

.log-item {
  display: flex;
  margin-bottom: 5px;
  font-family: monospace;
  font-size: 12px;
}

.log-time {
  color: #666;
  margin-right: 10px;
  min-width: 80px;
}

.log-message {
  color: #333;
}

h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
}

@media (max-width: 768px) {
  .debug-panel {
    grid-template-columns: 1fr;
  }
  
  .button-group {
    flex-direction: column;
  }
}
</style>
